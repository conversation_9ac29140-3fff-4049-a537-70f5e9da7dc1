import 'package:flutter/material.dart';
import 'package:stacked_services/stacked_services.dart';

class PackageDescriptionDialog extends StatefulWidget {
  final DialogRequest request;
  final Function(DialogResponse) completer;

  const PackageDescriptionDialog({
    super.key,
    required this.request,
    required this.completer,
  });

  @override
  State<PackageDescriptionDialog> createState() => _PackageDescriptionDialogState();
}

class _PackageDescriptionDialogState extends State<PackageDescriptionDialog> {
  String? selectedPackageType;

  final List<PackageType> packageTypes = [
    PackageType(
      id: 'box_carat',
      name: 'Box / Carat',
      icon: '📦',
      iconColor: Colors.blue,
    ),
    PackageType(
      id: 'personal_bag',
      name: 'Personal Bag / Luggage Bag',
      icon: '💼',
      iconColor: Colors.blue,
    ),
    PackageType(
      id: 'pouch_envelope',
      name: 'Pouch / Envelope',
      icon: '📄',
      iconColor: Colors.blue,
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Get current selected value from request data
    selectedPackageType = widget.request.data?['currentValue'];
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Text(
                    'Describe your package',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => widget.completer(DialogResponse(confirmed: false)),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),

            // Package Types List
            Flexible(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    const Text(
                      'Type of packages',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Package type options
                    ...packageTypes.map((packageType) => _buildPackageTypeOption(packageType)),
                  ],
                ),
              ),
            ),

            // Bottom padding
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageTypeOption(PackageType packageType) {
    final isSelected = selectedPackageType == packageType.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            setState(() {
              selectedPackageType = packageType.id;
            });

            // Return the selected package type
            widget.completer(DialogResponse(
              confirmed: true,
              data: {
                'selectedType': packageType.id,
                'selectedName': packageType.name,
              },
            ));
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue.shade50 : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.transparent,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Package Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: packageType.iconColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      packageType.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Package Name
                Expanded(
                  child: Text(
                    packageType.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.blue : Colors.black87,
                    ),
                  ),
                ),

                // Selection indicator
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Colors.blue,
                    size: 20,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class PackageType {
  final String id;
  final String name;
  final String icon;
  final Color iconColor;

  PackageType({
    required this.id,
    required this.name,
    required this.icon,
    required this.iconColor,
  });
}
