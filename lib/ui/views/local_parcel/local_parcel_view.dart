import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import 'local_parcel_viewmodel.dart';
import '../../common/app_colors.dart';
import '../../widgets/distance_calculator_widget.dart';
import '../../../models/package_item_model.dart';

class LocalParcelView extends StackedView<LocalParcelViewModel> {
  const LocalParcelView({super.key});

  @override
  Widget builder(
    BuildContext context,
    LocalParcelViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground, // Light grey background
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.black87,
            size: 24,
          ),
        ),
        title: const Text(
          'Local Parcel',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Progress Indicator Section
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: _buildProgressIndicator(viewModel.currentStep),
          ),

          // Main Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: _buildStepContent(viewModel),
            ),
          ),

          // Bottom Next Button
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(24),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: viewModel.canProceedToNext ? () {
                  debugPrint('Next button pressed');
                  viewModel.proceedToNextStep();
                } : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.buttonBackground, // Greyish black
                  foregroundColor: AppColors.buttonText, // Orange yellow
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      viewModel.currentStep == 3 ? 'Submit Order' : 'Next',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      viewModel.currentStep == 3 ? Icons.check_circle : Icons.arrow_forward,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(LocalParcelViewModel viewModel) {
    switch (viewModel.currentStep) {
      case 0:
        return _buildAddressStep(viewModel);
      case 1:
        return _buildPackageStep(viewModel);
      case 2:
        return _buildEstimateStep(viewModel);
      case 3:
        return _buildReviewStep(viewModel);
      default:
        return _buildAddressStep(viewModel);
    }
  }

  Widget _buildAddressStep(LocalParcelViewModel viewModel) {
    return Column(
      children: [
        // Show detailed address view if pickup address is selected
        if (viewModel.hasPickupAddress) ...[
          _buildDetailedAddressView(viewModel),
        ] else ...[
          // Show selection interface if pickup address not selected
          // Pickup Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_up,
            iconColor: AppColors.pickupIconColor,
            title: 'PICKUP AT',
            buttonText: 'Add pickup details',
            isAddressSelected: false,
            onPressed: () => viewModel.handlePickupAddressSelection(),
          ),

          const SizedBox(height: 24),

          // Delivery Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_down,
            iconColor: AppColors.deliveryIconColor,
            title: 'DELIVERY TO',
            buttonText: 'Add drop details',
            isAddressSelected: false,
            onPressed: () => viewModel.handleDeliveryAddressSelection(),
          ),
        ],

        // Distance information
        if (viewModel.distanceResult != null) ...[
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightBlueBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.route,
                  color: AppColors.borderColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Distance',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        viewModel.distanceResult!.distanceText,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.borderColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailedAddressView(LocalParcelViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Icons with connecting line
              Column(
                children: [
                  // Pickup icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.pickupIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_up,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  // Connecting dashed line
                  Container(
                    width: 2,
                    height: 60,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: CustomPaint(
                      painter: DashedLinePainter(color: Colors.grey[400]!),
                    ),
                  ),
                  // Delivery icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.deliveryIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              // Right side - Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pickup address
                    _buildAddressDetails(
                      title: 'Pickup Address',
                      address: viewModel.pickupAddressDisplay,
                      phone: viewModel.pickupDetails?['contactPhone'] ?? '',
                      onEdit: () => viewModel.handlePickupAddressSelection(),
                    ),
                    const SizedBox(height: 32),
                    // Delivery address
                    if (viewModel.hasDeliveryAddress) ...[
                      _buildAddressDetails(
                        title: 'Delivery Address',
                        address: viewModel.deliveryAddressDisplay,
                        phone: viewModel.deliveryDetails?['contactPhone'] ?? '',
                        onEdit: () => viewModel.handleDeliveryAddressSelection(),
                      ),
                    ] else ...[
                      _buildAddDeliveryButton(viewModel),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddressDetails({
    required String title,
    required String address,
    required String phone,
    required VoidCallback onEdit,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                letterSpacing: 0.5,
              ),
            ),
            GestureDetector(
              onTap: onEdit,
              child: const Text(
                'Edit',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          address,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
            height: 1.4,
          ),
          maxLines: null, // Allow unlimited lines
          softWrap: true, // Enable text wrapping
        ),
        if (phone.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Phone: $phone',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddDeliveryButton(LocalParcelViewModel viewModel) {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => viewModel.handleDeliveryAddressSelection(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.deliveryIconColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Add delivery details',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildPackageStep(LocalParcelViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Package Details Header
        const Text(
          'Package Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),

        // Package Items List
        ...viewModel.packageItems.map((item) => _buildPackageItem(viewModel, item)),

        // Add Item Button
        const SizedBox(height: 16),
        _buildAddItemButton(viewModel),
      ],
    );
  }

  Widget _buildEstimateStep(LocalParcelViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Schedule & Confirm Header
        const Text(
          'Schedule & Confirm',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 32),

        // Select Delivery Type Section
        const Text(
          'Select Delivery Type',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Delivery Type Options Row
        Row(
          children: [
            // Express Delivery Option (moved to left)
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.setDeliveryType('express'),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: viewModel.selectedDeliveryType == 'express'
                        ? AppColors.buttonBackground
                        : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: viewModel.selectedDeliveryType == 'express'
                          ? AppColors.borderColor
                          : AppColors.borderColor.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Express',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: viewModel.selectedDeliveryType == 'express'
                              ? AppColors.buttonText
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '₹80',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: viewModel.selectedDeliveryType == 'express'
                              ? AppColors.buttonText
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Delivery in 3 hours',
                        style: TextStyle(
                          fontSize: 12,
                          color: viewModel.selectedDeliveryType == 'express'
                              ? AppColors.buttonText.withValues(alpha: 0.8)
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Standard Delivery Option (moved to right)
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.setDeliveryType('standard'),
                child: Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: viewModel.selectedDeliveryType == 'standard'
                        ? AppColors.buttonBackground
                        : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: viewModel.selectedDeliveryType == 'standard'
                          ? AppColors.borderColor
                          : AppColors.borderColor.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Standard',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: viewModel.selectedDeliveryType == 'standard'
                              ? AppColors.buttonText
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '₹40',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: viewModel.selectedDeliveryType == 'standard'
                              ? AppColors.buttonText
                              : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Accepting between\n8 AM to 9 PM',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          color: viewModel.selectedDeliveryType == 'standard'
                              ? AppColors.buttonText.withValues(alpha: 0.8)
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        // Show schedule section only for standard delivery
        if (viewModel.selectedDeliveryType == 'standard') ...[
          const SizedBox(height: 32),

          // Schedule Your Pickup Section
          const Text(
            'Schedule Your Pickup',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),

          // Time and Date Selection Row
          Row(
          children: [
            // Enter Time
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.selectPickupTime(),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.borderColor),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enter Time',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.secondaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        viewModel.selectedPickupTime ?? 'Select time',
                        style: TextStyle(
                          fontSize: 16,
                          color: viewModel.selectedPickupTime != null
                              ? Colors.black87
                              : AppColors.secondaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Enter Date
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.selectPickupDate(),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.borderColor),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Enter Date',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.secondaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        viewModel.selectedPickupDate ?? 'Select date',
                        style: TextStyle(
                          fontSize: 16,
                          color: viewModel.selectedPickupDate != null
                              ? Colors.black87
                              : AppColors.secondaryText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        ], // Close the conditional block for standard delivery
      ],
    );
  }

  Widget _buildReviewStep(LocalParcelViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Error Message for Package Description
        if (viewModel.packageItems.any((item) => item.description.isEmpty)) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 24),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.info,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 12),
                Text(
                  'Package description is required',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],

        // 1. Order Items Section - First Container
        _buildReviewContainer(
          title: 'Order Items',
          onEdit: () => viewModel.goToStep(1),
          child: Column(
            children: [
              ...viewModel.packageItems.map((item) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.lightBlueBackground,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Item ${item.itemNumber} - ${item.dimension.name}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    if (item.description.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Type: ${item.description}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                    if (item.contents.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Contents: ${item.contents}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              )),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 2. Order Type Section - Second Container
        _buildReviewContainer(
          title: 'Order Type',
          onEdit: () => viewModel.goToStep(2),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${viewModel.selectedDeliveryType == 'express' ? 'Express' : 'Standard'} Delivery',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                viewModel.selectedDeliveryType == 'express'
                    ? '₹80 - Delivery in 3 hours'
                    : '₹40 - Accepting between 8 AM to 9 PM',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              if (viewModel.selectedDeliveryType == 'standard') ...[
                const SizedBox(height: 8),
                Text(
                  'Scheduled:',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  'Time: ${viewModel.selectedPickupTime ?? "Not selected"} | Date: ${viewModel.selectedPickupDate ?? "Not selected"}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 3. Pickup Address Section - Third Container
        _buildReviewContainer(
          title: 'Pickup Address',
          onEdit: () => viewModel.goToStep(0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (viewModel.hasPickupAddress) ...[
                Text(
                  'Address line -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.pickupDetails?['fullAddress'] ?? viewModel.pickupAddressDisplay,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Mobile Number -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.pickupDetails?['contactPhone'] ?? '9877',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Landmark -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.pickupDetails?['landmark'] ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'City -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Pincode -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ] else ...[
                Text(
                  'No pickup address selected',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 4. Delivery Address Section - Fourth Container
        _buildReviewContainer(
          title: 'Delivery Address',
          onEdit: () => viewModel.goToStep(0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (viewModel.hasDeliveryAddress) ...[
                Text(
                  'Address line -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.deliveryDetails?['fullAddress'] ?? viewModel.deliveryAddressDisplay,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Mobile Number -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.deliveryDetails?['contactPhone'] ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Landmark -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  viewModel.deliveryDetails?['landmark'] ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'City -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Pincode -',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ] else ...[
                Text(
                  'No delivery address selected',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 32),

        // Terms and Conditions Checkbox
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: viewModel.termsAccepted,
              onChanged: (value) {
                viewModel.toggleTermsAcceptance(value);
              },
              activeColor: AppColors.borderColor,
            ),
            const Expanded(
              child: Text(
                'I agree with terms & conditions of use, guarantee that the shipment does not contain any restricted items and meant for personal use',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(int currentStep) {
    final steps = [
      {'title': 'Address', 'icon': Icons.location_on},
      {'title': 'Package', 'icon': Icons.inventory_2_outlined},
      {'title': 'Estimate', 'icon': Icons.calendar_today_outlined},
      {'title': 'Review', 'icon': Icons.rate_review_outlined},
    ];

    return Row(
      children: List.generate(steps.length * 2 - 1, (index) {
        if (index.isEven) {
          // This is a step (icon + title)
          final stepIndex = index ~/ 2;
          final isActive = stepIndex == currentStep;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Column(
              children: [
                // Icon Circle
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.inactiveStepColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    steps[stepIndex]['icon'] as IconData,
                    color: isActive || isCompleted ? Colors.yellowAccent : Colors.grey[600],
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                // Step Title
                Text(
                  steps[stepIndex]['title'] as String,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step text
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          );
        } else {
          // This is a connecting line between steps
          final stepIndex = index ~/ 2;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Container(
              height: 2,
              margin: const EdgeInsets.only(bottom: 32), // Align with icon center
              child: CustomPaint(
                painter: DashedLinePainter(
                  color: isCompleted
                      ? AppColors.completedStepColor
                      : AppColors.inactiveStepColor,
                ),
              ),
            ),
          );
        }
      }),
    );
  }

  Widget _buildLocationSection({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String buttonText,
    required bool isAddressSelected,
    required VoidCallback onPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: iconColor,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.buttonBackground,
                foregroundColor: AppColors.buttonText,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    buttonText,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.add_circle_outline,
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDottedConnector() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          // Left side spacing to align with center of icons
          const SizedBox(width: 25), // Half of icon width (50/2) to center align
          // Dotted line - make it more prominent
          SizedBox(
            width: 4, // Increased width for better visibility
            height: 60, // Increased height for better connection
            child: CustomPaint(
              painter: DottedLinePainter(
                color: AppColors.borderColor,
                dashHeight: 6, // Longer dashes
                dashSpace: 3, // Smaller gaps
                strokeWidth: 3, // Thicker line
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageItem(LocalParcelViewModel viewModel, PackageItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header with count and remove button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Item ${item.itemNumber}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
              if (viewModel.packageItems.length > 1)
                TextButton(
                  onPressed: () => viewModel.removePackageItem(item.id),
                  child: const Text(
                    'Remove',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Dimension text
          const Text(
            'Dimension',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Dimension grid (2x2)
          _buildDimensionGrid(viewModel, item),
          const SizedBox(height: 20),

          // Describe your package
          const Text(
            'Describe your package',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => viewModel.showPackageDescriptionDialog(item.id),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderColor),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Text(
                item.description.isEmpty
                    ? 'Select package type'
                    : item.description,
                style: TextStyle(
                  fontSize: 16,
                  color: item.description.isEmpty
                      ? Colors.grey.shade600
                      : Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Select contents
          const Text(
            'Select contents',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => viewModel.showPackageContentsDialog(item.id),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderColor),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Text(
                item.contents.isEmpty
                    ? 'Select package contents'
                    : item.contents,
                style: TextStyle(
                  fontSize: 16,
                  color: item.contents.isEmpty
                      ? Colors.grey.shade600
                      : Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Package Value
          TextFormField(
            initialValue: item.value > 0 ? item.value.toString() : '',
            decoration: InputDecoration(
              hintText: 'Package Value (Max : Rs 49999)',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final doubleValue = double.tryParse(value) ?? 0.0;
              if (doubleValue <= 49999) {
                viewModel.updatePackageValue(item.id, doubleValue);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDimensionGrid(LocalParcelViewModel viewModel, PackageItemModel item) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: viewModel.availableDimensions.length,
      itemBuilder: (context, index) {
        final dimension = viewModel.availableDimensions[index];
        final isSelected = item.dimension.id == dimension.id;

        return GestureDetector(
          onTap: () => viewModel.updatePackageDimension(item.id, dimension),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? Colors.lightBlue.shade100 : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue : AppColors.borderColor,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  dimension.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.blue : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  dimension.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.blue.shade700 : Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddItemButton(LocalParcelViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton.icon(
        onPressed: () => viewModel.addPackageItem(),
        icon: const Icon(Icons.add, color: Colors.blue),
        label: const Text(
          'Add Item +',
          style: TextStyle(
            color: Colors.blue,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.blue),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  // Helper method to build review container with edit option
  Widget _buildReviewContainer({
    required String title,
    required VoidCallback onEdit,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header with Edit Button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: onEdit,
                style: TextButton.styleFrom(
                  foregroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
                child: const Text(
                  'Edit',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Section Content
          child,
        ],
      ),
    );
  }

  // Helper method to build review section with edit option
  Widget _buildReviewSection({
    required String title,
    required VoidCallback onEdit,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header with edit button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: onEdit,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  backgroundColor: AppColors.lightBlueBackground,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: Text(
                  'Edit',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.borderColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  // Helper method to build simple review section with edit option
  Widget _buildSimpleReviewSection({
    required String title,
    required VoidCallback onEdit,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header with Edit Button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: onEdit,
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              child: const Text(
                'Edit',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Section Content
        child,
      ],
    );
  }

  // Helper method to build review package item
  Widget _buildReviewPackageItem(LocalParcelViewModel viewModel, PackageItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightBlueBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Item ${item.itemNumber}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.borderColor,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.buttonBackground,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  item.dimension.name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.buttonText,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Package details
          if (item.description.isNotEmpty) ...[
            _buildReviewDetailRow('Type:', item.description),
            const SizedBox(height: 8),
          ],
          if (item.contents.isNotEmpty) ...[
            _buildReviewDetailRow('Contents:', item.contents),
            const SizedBox(height: 8),
          ],
          _buildReviewDetailRow('Weight Limit:', item.dimension.weightLimit),
          if (item.value > 0) ...[
            const SizedBox(height: 8),
            _buildReviewDetailRow('Value:', '₹${item.value.toStringAsFixed(0)}'),
          ],
        ],
      ),
    );
  }

  // Helper method to build review address item
  Widget _buildReviewAddressItem({
    required String title,
    required String address,
    required Color iconColor,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightBlueBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: iconColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.borderColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  address,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build review detail item
  Widget _buildReviewDetailItem({
    required String label,
    required String value,
    required IconData icon,
    required Color iconColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.lightBlueBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.borderColor,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build review detail row
  Widget _buildReviewDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.borderColor,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to calculate total amount
  String _calculateTotalAmount(LocalParcelViewModel viewModel) {
    // Base delivery cost (Express: ₹80)
    double total = 80.0;

    // Add any additional costs based on package items
    for (var item in viewModel.packageItems) {
      // Add item value if any
      total += item.value;
    }

    return total.toStringAsFixed(0);
  }

  @override
  LocalParcelViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      LocalParcelViewModel();
}

class DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashHeight;
  final double dashSpace;
  final double strokeWidth;

  DottedLinePainter({
    this.color = Colors.grey,
    this.dashHeight = 4,
    this.dashSpace = 4,
    this.strokeWidth = 2,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startY = 0;
    final centerX = size.width / 2;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(centerX, startY),
        Offset(centerX, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = 4;
    const dashSpace = 4;
    double startX = 0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
