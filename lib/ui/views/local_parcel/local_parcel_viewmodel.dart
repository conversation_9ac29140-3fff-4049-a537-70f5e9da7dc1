import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../app/app.locator.dart';
import '../../../models/address_model.dart';
import '../../../models/package_item_model.dart';
import '../../../services/address_service.dart';
import '../../../services/distance_service.dart';
import '../map_picker/map_picker_view.dart';
import '../../setup_dialog_ui.dart';
import '../../../debug/distance_test.dart';
import '../../../debug/google_maps_test.dart';

class LocalParcelViewModel extends BaseViewModel {
  final DialogService _dialogService = locator<DialogService>();
  final AddressService _addressService = AddressService();

  // Constructor - test Google Maps integration
  LocalParcelViewModel() {
    GoogleMapsTestHelper.logImplementationStatus();
    _initializePackageItems();
  }

  // Initialize with first package item
  void _initializePackageItems() {
    _packageItems = [
      PackageItemModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        itemNumber: 1,
        dimension: _availableDimensions.first,
      ),
    ];
  }

  // Current step in the process (0: Address, 1: Package, 2: Estimate, 3: Review)
  int _currentStep = 0;
  int get currentStep => _currentStep;

  // Pickup and delivery details
  Map<String, dynamic>? _pickupDetails;
  Map<String, dynamic>? _deliveryDetails;
  Map<String, dynamic>? _packageDetails;
  Map<String, dynamic>? _estimateDetails;
  DistanceResult? _distanceResult;

  // Estimate step properties
  String? _selectedPickupTime;
  String? _selectedPickupDate;

  String? get selectedPickupTime => _selectedPickupTime;
  String? get selectedPickupDate => _selectedPickupDate;

  // Review step properties
  bool _termsAccepted = false;
  bool get termsAccepted => _termsAccepted;

  // Package items management
  List<PackageItemModel> _packageItems = [];
  final List<PackageDimension> _availableDimensions = PackageDimension.getDefaultDimensions();

  Map<String, dynamic>? get pickupDetails => _pickupDetails;
  Map<String, dynamic>? get deliveryDetails => _deliveryDetails;
  Map<String, dynamic>? get packageDetails => _packageDetails;
  Map<String, dynamic>? get estimateDetails => _estimateDetails;
  DistanceResult? get distanceResult => _distanceResult;

  // Package getters
  List<PackageItemModel> get packageItems => _packageItems;
  List<PackageDimension> get availableDimensions => _availableDimensions;
  bool get hasPackageItems => _packageItems.isNotEmpty;

  // Check if user can proceed to next step
  bool get canProceedToNext {
    switch (_currentStep) {
      case 0: // Address step
        return hasPickupAddress && hasDeliveryAddress && _distanceResult != null;
      case 1: // Package step
        return hasPackageItems && _packageItems.every((item) =>
          item.dimension.id.isNotEmpty &&
          item.value > 0 &&
          item.description.isNotEmpty &&
          item.contents.isNotEmpty
        );
      case 2: // Estimate step
        return _selectedPickupTime != null && _selectedPickupDate != null;
      case 3: // Review step
        return _termsAccepted;
      default:
        return false;
    }
  }

  // Set pickup details
  void setPickupDetails(Map<String, dynamic> details) {
    _pickupDetails = details;
    debugPrint('Pickup details set: $details');
    rebuildUi();
  }

  // Set delivery details
  void setDeliveryDetails(Map<String, dynamic> details) {
    _deliveryDetails = details;
    debugPrint('Delivery details set: $details');
    rebuildUi();
  }

  // Set package details
  void setPackageDetails(Map<String, dynamic> details) {
    _packageDetails = details;
    debugPrint('Package details set: $details');
    rebuildUi();
  }

  // Set estimate details
  void setEstimateDetails(Map<String, dynamic> details) {
    _estimateDetails = details;
    debugPrint('Estimate details set: $details');
    rebuildUi();
  }

  // Move to next step
  void nextStep() {
    if (canProceedToNext && _currentStep < 3) {
      _currentStep++;
      debugPrint('Moved to step: $_currentStep');
      rebuildUi();
    }
  }

  // Move to previous step
  void previousStep() {
    if (_currentStep > 0) {
      _currentStep--;
      debugPrint('Moved back to step: $_currentStep');
      rebuildUi();
    }
  }

  // Go to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 3) {
      _currentStep = step;
      debugPrint('Jumped to step: $_currentStep');
      rebuildUi();
    }
  }

  // Reset all data
  void resetData() {
    _currentStep = 0;
    _pickupDetails = null;
    _deliveryDetails = null;
    _packageDetails = null;
    _estimateDetails = null;
    _selectedPickupTime = null;
    _selectedPickupDate = null;
    debugPrint('Local parcel data reset');
    rebuildUi();
  }

  // Select pickup time
  Future<void> selectPickupTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      _selectedPickupTime = picked.format(StackedService.navigatorKey!.currentContext!);
      debugPrint('Selected pickup time: $_selectedPickupTime');
      rebuildUi();
    }
  }

  // Select pickup date
  Future<void> selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (picked != null) {
      _selectedPickupDate = '${picked.day}/${picked.month}/${picked.year}';
      debugPrint('Selected pickup date: $_selectedPickupDate');
      rebuildUi();
    }
  }

  // Get step title
  String getStepTitle() {
    switch (_currentStep) {
      case 0:
        return 'Address Details';
      case 1:
        return 'Package Information';
      case 2:
        return 'Price Estimate';
      case 3:
        return 'Review & Confirm';
      default:
        return 'Local Parcel';
    }
  }

  // Check if step is completed
  bool isStepCompleted(int step) {
    switch (step) {
      case 0:
        return _pickupDetails != null && _deliveryDetails != null;
      case 1:
        return _packageDetails != null;
      case 2:
        return _estimateDetails != null;
      case 3:
        return false; // Review step is never "completed" until final submission
      default:
        return false;
    }
  }

  // Get completion percentage
  double get completionPercentage {
    int completedSteps = 0;
    for (int i = 0; i <= 3; i++) {
      if (isStepCompleted(i)) {
        completedSteps++;
      }
    }
    return completedSteps / 4.0;
  }

  // Handle pickup address selection
  Future<void> handlePickupAddressSelection() async {
    debugPrint('Handling pickup address selection');
    await _showAddressSelectionDialog(isPickup: true);
  }

  // Handle delivery address selection
  Future<void> handleDeliveryAddressSelection() async {
    debugPrint('Handling delivery address selection');
    await _showAddressSelectionDialog(isPickup: false);
  }

  // Show address selection dialog
  Future<void> _showAddressSelectionDialog({required bool isPickup}) async {
    try {
      // Check if there are saved addresses
      final savedAddresses = await _addressService.getSavedAddresses();

      if (savedAddresses.isEmpty) {
        // No saved addresses, go directly to map picker
        debugPrint('No saved addresses found, opening map picker');
        await _openMapPicker(isPickup: isPickup);
      } else {
        // Show address selection dialog
        debugPrint('Showing address selection dialog');
        final response = await _dialogService.showCustomDialog(
          variant: DialogType.addressSelection,
          title: 'Select Address',
          description: 'Choose from saved addresses or add new one',
        );

        if (response?.confirmed == true && response?.data != null) {
          final action = response!.data['action'];

          switch (action) {
            case 'select':
              // Address selected from saved addresses
              final addressData = response.data['address'];
              final address = AddressModel.fromJson(addressData);
              _handleAddressSelected(address, isPickup: isPickup);
              break;

            case 'add_new':
              // Add new address
              await _openMapPicker(isPickup: isPickup);
              break;

            case 'edit':
              // Edit existing address
              final addressData = response.data['address'];
              final address = AddressModel.fromJson(addressData);
              await _openMapPicker(isPickup: isPickup, editAddress: address);
              break;
          }
        }
      }
    } catch (e) {
      debugPrint('Error in address selection: $e');
    }
  }

  // Open map picker
  Future<void> _openMapPicker({
    required bool isPickup,
    AddressModel? editAddress,
  }) async {
    try {
      final context = StackedService.navigatorKey?.currentContext;
      if (context != null) {
        final result = await Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => MapPickerView(editAddress: editAddress),
        ));

        if (result is AddressModel) {
          _handleAddressSelected(result, isPickup: isPickup);
        }
      }
    } catch (e) {
      debugPrint('Error opening map picker: $e');
    }
  }

  // Handle address selected
  void _handleAddressSelected(AddressModel address, {required bool isPickup}) {
    final addressDetails = {
      'id': address.id,
      'title': address.title,
      'fullAddress': address.fullAddress,
      'landmark': address.landmark,
      'latitude': address.latitude,
      'longitude': address.longitude,
      'addressType': address.addressType,
      'contactName': address.contactName,
      'contactPhone': address.contactPhone,
    };

    if (isPickup) {
      setPickupDetails(addressDetails);
      debugPrint('Pickup address selected: ${address.title}');
      DistanceTestHelper.testAddressCoordinates(addressDetails);
    } else {
      setDeliveryDetails(addressDetails);
      debugPrint('Delivery address selected: ${address.title}');
      DistanceTestHelper.testAddressCoordinates(addressDetails);
    }

    // Debug distance calculation when both addresses are available
    if (_pickupDetails != null && _deliveryDetails != null) {
      debugPrint('🎯 Both addresses available, testing distance calculation...');
      DistanceTestHelper.debugDistanceWidget(
        pickupLat: _pickupDetails!['latitude']?.toDouble(),
        pickupLng: _pickupDetails!['longitude']?.toDouble(),
        dropLat: _deliveryDetails!['latitude']?.toDouble(),
        dropLng: _deliveryDetails!['longitude']?.toDouble(),
        pickupAddress: _pickupDetails!['fullAddress'],
        dropAddress: _deliveryDetails!['fullAddress'],
      );
    }
  }

  // Get pickup address display text
  String get pickupAddressDisplay {
    if (_pickupDetails == null) return '';
    return _pickupDetails!['title'] ?? 'Selected Address';
  }

  // Get delivery address display text
  String get deliveryAddressDisplay {
    if (_deliveryDetails == null) return '';
    return _deliveryDetails!['title'] ?? 'Selected Address';
  }

  // Check if pickup address is selected
  bool get hasPickupAddress => _pickupDetails != null;

  // Check if delivery address is selected
  bool get hasDeliveryAddress => _deliveryDetails != null;

  // Set distance result
  void setDistanceResult(DistanceResult result) {
    _distanceResult = result;
    debugPrint('Distance calculated: ${result.distanceText}');
    rebuildUi();
  }

  // Proceed to next step
  void proceedToNextStep() {
    if (canProceedToNext) {
      if (_currentStep == 3) {
        // Final step - submit order
        _submitOrder();
      } else {
        _currentStep++;
        debugPrint('Proceeding to step: $_currentStep');
        rebuildUi();
      }
    }
  }

  // Submit order and show success dialog
  Future<void> _submitOrder() async {
    try {
      debugPrint('Submitting order...');

      // Simulate order submission (replace with actual API call)
      await Future.delayed(const Duration(milliseconds: 500));

      // Generate order ID
      final orderId = 'WG${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

      // Calculate total price (base price is ₹80 for express delivery)
      const basePrice = 80;
      final totalPrice = '₹$basePrice';

      debugPrint('Order submitted successfully. Order ID: $orderId, Price: $totalPrice');

      // Show success dialog
      final response = await _dialogService.showCustomDialog(
        variant: DialogType.orderSuccess,
        title: 'Order Successful',
        data: {
          'orderId': orderId,
          'price': totalPrice,
        },
      );

      if (response?.confirmed == true) {
        // Navigate back to home or reset the form
        _resetToHome();
      }
    } catch (e) {
      debugPrint('Error submitting order: $e');
      // Handle error - could show error dialog
    }
  }

  // Reset to home after successful order
  void _resetToHome() {
    // Reset all data
    resetData();

    // Navigate back to home
    final navigationService = locator<NavigationService>();
    navigationService.back();

    debugPrint('Order completed, navigated back to home');
  }

  // Package management methods
  void addPackageItem() {
    final newItem = PackageItemModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      itemNumber: _packageItems.length + 1,
      dimension: _availableDimensions.first,
    );
    _packageItems.add(newItem);
    debugPrint('Added package item ${newItem.itemNumber}');
    rebuildUi();
  }

  void removePackageItem(String itemId) {
    _packageItems.removeWhere((item) => item.id == itemId);
    // Renumber remaining items
    for (int i = 0; i < _packageItems.length; i++) {
      _packageItems[i] = _packageItems[i].copyWith(itemNumber: i + 1);
    }
    debugPrint('Removed package item, remaining: ${_packageItems.length}');
    rebuildUi();
  }

  void updatePackageDimension(String itemId, PackageDimension dimension) {
    final index = _packageItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _packageItems[index] = _packageItems[index].copyWith(dimension: dimension);
      debugPrint('Updated item ${_packageItems[index].itemNumber} dimension to ${dimension.name}');
      rebuildUi();
    }
  }

  void updatePackageDescription(String itemId, String description) {
    final index = _packageItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _packageItems[index] = _packageItems[index].copyWith(description: description);
      rebuildUi();
    }
  }

  void updatePackageContents(String itemId, String contents) {
    final index = _packageItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _packageItems[index] = _packageItems[index].copyWith(contents: contents);
      rebuildUi();
    }
  }

  void updatePackageValue(String itemId, double value) {
    final index = _packageItems.indexWhere((item) => item.id == itemId);
    if (index != -1) {
      _packageItems[index] = _packageItems[index].copyWith(value: value);
      rebuildUi();
    }
  }

  // Show package description dialog
  Future<void> showPackageDescriptionDialog(String itemId) async {
    final currentItem = _packageItems.firstWhere((item) => item.id == itemId);

    final response = await _dialogService.showCustomDialog(
      variant: DialogType.packageDescription,
      title: 'Describe your package',
      data: {'currentValue': currentItem.description},
    );

    if (response?.confirmed == true && response?.data != null) {
      final selectedName = response!.data!['selectedName'] as String;
      updatePackageDescription(itemId, selectedName);
      debugPrint('Package description updated to: $selectedName');
    }
  }

  // Show package contents dialog
  Future<void> showPackageContentsDialog(String itemId) async {
    final currentItem = _packageItems.firstWhere((item) => item.id == itemId);

    final response = await _dialogService.showCustomDialog(
      variant: DialogType.packageContents,
      title: 'Describe your package',
      data: {'currentContents': currentItem.contents},
    );

    if (response?.confirmed == true && response?.data != null) {
      final displayText = response!.data!['displayText'] as String;
      updatePackageContents(itemId, displayText);
      debugPrint('Package contents updated to: $displayText');
    }
  }

  // Get distance display text
  String get distanceDisplay {
    if (_distanceResult == null) return '';
    return _distanceResult!.distanceText;
  }

  // Toggle terms acceptance
  void toggleTermsAcceptance(bool? value) {
    _termsAccepted = value ?? false;
    debugPrint('Terms accepted: $_termsAccepted');
    rebuildUi();
  }
}
