import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:stacked_services/stacked_services.dart';
import '../../../app/app.locator.dart';
import '../../../models/address_model.dart';
import '../../../models/package_item_model.dart';
import '../../../services/address_service.dart';
import '../../../services/distance_service.dart';
import '../map_picker/map_picker_view.dart';
import '../../setup_dialog_ui.dart';
import '../../../debug/google_maps_test.dart';

class AllIndiaParcelViewModel extends BaseViewModel {
  final DialogService _dialogService = locator<DialogService>();

  // Constructor - test Google Maps integration
  AllIndiaParcelViewModel() {
    GoogleMapsTestHelper.logImplementationStatus();
    _initializePackageItems();
  }

  // Initialize with first package item
  void _initializePackageItems() {
    _packageItems = [
      PackageItemModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        itemNumber: 1,
        dimension: _availableDimensions.first,
      ),
    ];
  }

  // Current step in the process (0: Address, 1: Package, 2: Estimate, 3: Review)
  int _currentStep = 0;
  int get currentStep => _currentStep;

  // Pickup and delivery details
  Map<String, dynamic>? _pickupDetails;
  Map<String, dynamic>? _deliveryDetails;
  Map<String, dynamic>? _packageDetails;
  Map<String, dynamic>? _estimateDetails;
  DistanceResult? _distanceResult;

  // Estimate step properties
  String? _selectedPickupTime;
  String? _selectedPickupDate;

  String? get selectedPickupTime => _selectedPickupTime;
  String? get selectedPickupDate => _selectedPickupDate;

  // Review step properties
  bool _termsAccepted = false;
  bool get termsAccepted => _termsAccepted;

  // Package items management
  List<PackageItemModel> _packageItems = [];
  final List<PackageDimension> _availableDimensions = PackageDimension.getDefaultDimensions();

  Map<String, dynamic>? get pickupDetails => _pickupDetails;
  Map<String, dynamic>? get deliveryDetails => _deliveryDetails;
  Map<String, dynamic>? get packageDetails => _packageDetails;
  Map<String, dynamic>? get estimateDetails => _estimateDetails;
  DistanceResult? get distanceResult => _distanceResult;

  // Package getters
  List<PackageItemModel> get packageItems => _packageItems;
  List<PackageDimension> get availableDimensions => _availableDimensions;
  bool get hasPackageItems => _packageItems.isNotEmpty;

  // Check if user can proceed to next step
  bool get canProceedToNext {
    switch (_currentStep) {
      case 0: // Address step
        return hasPickupAddress && hasDeliveryAddress && _distanceResult != null;
      case 1: // Package step
        return hasPackageItems && _packageItems.every((item) =>
          item.dimension.id.isNotEmpty &&
          item.value > 0 &&
          item.description.isNotEmpty &&
          item.contents.isNotEmpty
        );
      case 2: // Estimate step
        return _selectedPickupTime != null && _selectedPickupDate != null;
      case 3: // Review step
        return _termsAccepted;
      default:
        return false;
    }
  }

  // Set pickup details
  void setPickupDetails(Map<String, dynamic> details) {
    _pickupDetails = details;
    debugPrint('All India Parcel - Pickup details set: $details');
    rebuildUi();
  }

  // Set delivery details
  void setDeliveryDetails(Map<String, dynamic> details) {
    _deliveryDetails = details;
    debugPrint('All India Parcel - Delivery details set: $details');
    rebuildUi();
  }

  // Address getters
  bool get hasPickupAddress => _pickupDetails != null;
  bool get hasDeliveryAddress => _deliveryDetails != null;

  String get pickupAddressDisplay {
    if (_pickupDetails == null) return '';
    return _pickupDetails!['fullAddress'] ?? 'Address not available';
  }

  String get deliveryAddressDisplay {
    if (_deliveryDetails == null) return '';
    return _deliveryDetails!['fullAddress'] ?? 'Address not available';
  }

  // Move to next step
  void proceedToNextStep() {
    if (canProceedToNext) {
      if (_currentStep == 3) {
        // Submit order on final step
        _submitOrder();
      } else {
        _currentStep++;
        debugPrint('All India Parcel - Moved to step: $_currentStep');
        rebuildUi();
      }
    }
  }

  // Move to previous step
  void previousStep() {
    if (_currentStep > 0) {
      _currentStep--;
      debugPrint('All India Parcel - Moved back to step: $_currentStep');
      rebuildUi();
    }
  }

  // Go to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 3) {
      _currentStep = step;
      debugPrint('All India Parcel - Jumped to step: $_currentStep');
      rebuildUi();
    }
  }

  // Reset all data
  void resetData() {
    _currentStep = 0;
    _pickupDetails = null;
    _deliveryDetails = null;
    _packageDetails = null;
    _estimateDetails = null;
    _selectedPickupTime = null;
    _selectedPickupDate = null;
    debugPrint('All India Parcel data reset');
    rebuildUi();
  }

  // Select pickup time
  Future<void> selectPickupTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null) {
      _selectedPickupTime = picked.format(StackedService.navigatorKey!.currentContext!);
      debugPrint('All India Parcel - Selected pickup time: $_selectedPickupTime');
      rebuildUi();
    }
  }

  // Select pickup date
  Future<void> selectPickupDate() async {
    final DateTime? picked = await showDatePicker(
      context: StackedService.navigatorKey!.currentContext!,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (picked != null) {
      _selectedPickupDate = '${picked.day}/${picked.month}/${picked.year}';
      debugPrint('All India Parcel - Selected pickup date: $_selectedPickupDate');
      rebuildUi();
    }
  }

  // Get step title
  String getStepTitle() {
    switch (_currentStep) {
      case 0:
        return 'Address Details';
      case 1:
        return 'Package Information';
      case 2:
        return 'Price Estimate';
      case 3:
        return 'Review & Confirm';
      default:
        return 'All India Parcel';
    }
  }

  // Check if step is completed
  bool isStepCompleted(int step) {
    switch (step) {
      case 0:
        return _pickupDetails != null && _deliveryDetails != null;
      case 1:
        return _packageDetails != null;
      case 2:
        return _estimateDetails != null;
      case 3:
        return false; // Review step is never "completed" until final submission
      default:
        return false;
    }
  }

  // Get completion percentage
  double get completionPercentage {
    int completedSteps = 0;
    for (int i = 0; i <= 3; i++) {
      if (isStepCompleted(i)) {
        completedSteps++;
      }
    }
    return completedSteps / 4.0;
  }

  // Handle pickup address selection
  Future<void> handlePickupAddressSelection() async {
    debugPrint('All India Parcel - Handling pickup address selection');
    await _showAddressSelectionDialog(isPickup: true);
  }

  // Handle delivery address selection
  Future<void> handleDeliveryAddressSelection() async {
    debugPrint('All India Parcel - Handling delivery address selection');
    await _showAddressSelectionDialog(isPickup: false);
  }

  // Show address selection dialog
  Future<void> _showAddressSelectionDialog({required bool isPickup}) async {
    try {
      // Check if there are saved addresses first
      final addressService = AddressService();
      final savedAddresses = await addressService.getSavedAddresses();

      if (savedAddresses.isEmpty) {
        // No saved addresses, go directly to map picker
        debugPrint('All India Parcel - No saved addresses found, opening map picker');
        await _openMapPicker(isPickup: isPickup);
        return;
      }

      // Show address selection dialog
      debugPrint('All India Parcel - Showing address selection dialog');
      final result = await _dialogService.showCustomDialog(
        variant: DialogType.addressSelection,
        title: isPickup ? 'Select Pickup Address' : 'Select Delivery Address',
        data: {
          'isPickup': isPickup,
          'currentAddress': isPickup ? _pickupDetails : _deliveryDetails,
        },
      );

      if (result?.confirmed == true && result?.data != null) {
        final addressData = result!.data as Map<String, dynamic>;
        final action = addressData['action'];

        switch (action) {
          case 'select':
            // Address selected from saved addresses
            final address = addressData['address'];
            if (isPickup) {
              setPickupDetails(address);
            } else {
              setDeliveryDetails(address);
            }

            // Calculate distance if both addresses are selected
            if (hasPickupAddress && hasDeliveryAddress) {
              await _calculateDistance();
            }
            break;

          case 'add_new':
            // Add new address - open map picker
            await _openMapPicker(isPickup: isPickup);
            break;

          case 'edit':
            // Edit existing address - open map picker with address data
            final address = AddressModel.fromJson(addressData['address']);
            await _openMapPicker(isPickup: isPickup, editAddress: address);
            break;
        }
      }
    } catch (e) {
      debugPrint('All India Parcel - Error in address selection: $e');
    }
  }

  // Open map picker for address selection
  Future<void> _openMapPicker({required bool isPickup, AddressModel? editAddress}) async {
    try {
      final context = StackedService.navigatorKey?.currentContext;
      if (context != null) {
        final result = await Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => MapPickerView(editAddress: editAddress),
        ));

        if (result is AddressModel) {
          _handleAddressSelected(result, isPickup: isPickup);
        }
      }
    } catch (e) {
      debugPrint('All India Parcel - Error opening map picker: $e');
    }
  }

  // Handle address selected from map picker
  void _handleAddressSelected(AddressModel address, {required bool isPickup}) {
    final addressDetails = {
      'id': address.id,
      'title': address.title,
      'fullAddress': address.fullAddress,
      'landmark': address.landmark,
      'latitude': address.latitude,
      'longitude': address.longitude,
      'addressType': address.addressType,
      'contactName': address.contactName,
      'contactPhone': address.contactPhone,
    };

    if (isPickup) {
      setPickupDetails(addressDetails);
      debugPrint('All India Parcel - Pickup address selected: ${address.title}');
    } else {
      setDeliveryDetails(addressDetails);
      debugPrint('All India Parcel - Delivery address selected: ${address.title}');
    }

    // Calculate distance if both addresses are available
    if (hasPickupAddress && hasDeliveryAddress) {
      _calculateDistance();
    }
  }

  // Calculate distance between pickup and delivery
  Future<void> _calculateDistance() async {
    if (!hasPickupAddress || !hasDeliveryAddress) return;

    try {
      debugPrint('All India Parcel - Calculating distance...');

      final pickupLat = _pickupDetails!['latitude'] as double?;
      final pickupLng = _pickupDetails!['longitude'] as double?;
      final deliveryLat = _deliveryDetails!['latitude'] as double?;
      final deliveryLng = _deliveryDetails!['longitude'] as double?;

      if (pickupLat != null && pickupLng != null && deliveryLat != null && deliveryLng != null) {
        final distanceService = DistanceService();
        _distanceResult = await distanceService.calculateRoadDistance(
          originLat: pickupLat,
          originLng: pickupLng,
          destinationLat: deliveryLat,
          destinationLng: deliveryLng,
        );

        debugPrint('All India Parcel - Distance calculated: ${_distanceResult?.distance}');
        rebuildUi();
      }
    } catch (e) {
      debugPrint('All India Parcel - Error calculating distance: $e');
    }
  }

  // Submit order and show success dialog
  Future<void> _submitOrder() async {
    try {
      debugPrint('All India Parcel - Submitting order...');

      // Simulate order submission (replace with actual API call)
      await Future.delayed(const Duration(milliseconds: 500));

      // Generate order ID
      final orderId = 'AIP${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';

      // Calculate total price (base price is ₹150 for All India delivery)
      const basePrice = 150;
      final totalPrice = '₹$basePrice';

      debugPrint('All India Parcel - Order submitted successfully. Order ID: $orderId, Price: $totalPrice');

      // Show success dialog
      final response = await _dialogService.showCustomDialog(
        variant: DialogType.orderSuccess,
        title: 'Order Successful',
        data: {
          'orderId': orderId,
          'price': totalPrice,
        },
      );

      if (response?.confirmed == true) {
        // Navigate back to home or reset the form
        _resetToHome();
      }
    } catch (e) {
      debugPrint('All India Parcel - Error submitting order: $e');
      // Handle error - could show error dialog
    }
  }

  // Reset to home after successful order
  void _resetToHome() {
    // Reset all data
    resetData();

    // Navigate back to home
    final navigationService = locator<NavigationService>();
    navigationService.back();

    debugPrint('All India Parcel - Order completed, navigated back to home');
  }

  // Package management methods
  void addPackageItem() {
    final newItem = PackageItemModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      itemNumber: _packageItems.length + 1,
      dimension: _availableDimensions.first,
    );
    _packageItems.add(newItem);
    debugPrint('All India Parcel - Added package item: ${newItem.id}');
    rebuildUi();
  }

  void removePackageItem(String itemId) {
    _packageItems.removeWhere((item) => item.id == itemId);
    // Renumber remaining items
    for (int i = 0; i < _packageItems.length; i++) {
      _packageItems[i] = _packageItems[i].copyWith(itemNumber: i + 1);
    }
    debugPrint('All India Parcel - Removed package item: $itemId');
    rebuildUi();
  }

  void updatePackageItem(PackageItemModel updatedItem) {
    final index = _packageItems.indexWhere((item) => item.id == updatedItem.id);
    if (index != -1) {
      _packageItems[index] = updatedItem;
      debugPrint('All India Parcel - Updated package item: ${updatedItem.id}');
      rebuildUi();
    }
  }

  // Show package description dialog
  Future<void> showPackageDescriptionDialog(String itemId) async {
    try {
      final result = await _dialogService.showCustomDialog(
        variant: DialogType.packageDescription,
        title: 'Select Package Type',
        data: {'itemId': itemId},
      );

      if (result?.confirmed == true && result?.data != null) {
        final data = result!.data as Map<String, dynamic>;
        final description = data['selectedName'] as String? ?? '';

        final item = _packageItems.firstWhere((item) => item.id == itemId);
        final updatedItem = item.copyWith(description: description);
        updatePackageItem(updatedItem);
        debugPrint('All India Parcel - Package description updated to: $description');
      }
    } catch (e) {
      debugPrint('All India Parcel - Error showing package description dialog: $e');
    }
  }

  // Show package contents dialog
  Future<void> showPackageContentsDialog(String itemId) async {
    try {
      final result = await _dialogService.showCustomDialog(
        variant: DialogType.packageContents,
        title: 'Select Contents',
        data: {'itemId': itemId},
      );

      if (result?.confirmed == true && result?.data != null) {
        final data = result!.data as Map<String, dynamic>;
        final contents = data['displayText'] as String? ?? '';

        final item = _packageItems.firstWhere((item) => item.id == itemId);
        final updatedItem = item.copyWith(contents: contents);
        updatePackageItem(updatedItem);
        debugPrint('All India Parcel - Package contents updated to: $contents');
      }
    } catch (e) {
      debugPrint('All India Parcel - Error showing package contents dialog: $e');
    }
  }

  // Update package dimension
  void updatePackageDimension(String itemId, PackageDimension dimension) {
    final item = _packageItems.firstWhere((item) => item.id == itemId);
    final updatedItem = item.copyWith(dimension: dimension);
    updatePackageItem(updatedItem);
  }

  // Update package value
  void updatePackageValue(String itemId, double value) {
    final item = _packageItems.firstWhere((item) => item.id == itemId);
    final updatedItem = item.copyWith(value: value);
    updatePackageItem(updatedItem);
  }

  // Toggle terms acceptance
  void toggleTermsAcceptance(bool? value) {
    _termsAccepted = value ?? false;
    debugPrint('All India Parcel - Terms accepted: $_termsAccepted');
    rebuildUi();
  }
}
