import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';

import 'all_india_parcel_viewmodel.dart';
import '../../common/app_colors.dart';
import '../../../models/package_item_model.dart';
import 'steps/all_india_estimate_step.dart';
import 'steps/all_india_review_step.dart';

class AllIndiaParcelView extends StackedView<AllIndiaParcelViewModel> {
  const AllIndiaParcelView({super.key});

  @override
  Widget builder(
    BuildContext context,
    AllIndiaParcelViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground, // Light grey background
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.black87,
            size: 24,
          ),
        ),
        title: const Text(
          'All India Parcel',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        centerTitle: false,
      ),
      body: Column(
        children: [
          // Progress Indicator Section
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: _buildProgressIndicator(viewModel.currentStep),
          ),

          // Main Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: _buildStepContent(viewModel, context),
            ),
          ),

          // Bottom Navigation
          Container(
            padding: const EdgeInsets.all(24),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: viewModel.canProceedToNext ? () => viewModel.proceedToNextStep() : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: viewModel.canProceedToNext
                      ? AppColors.buttonBackground
                      : Colors.grey[400],
                  foregroundColor: viewModel.canProceedToNext
                      ? AppColors.buttonText
                      : Colors.grey[600],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      viewModel.currentStep == 2 ? 'Submit Order' : 'Next',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      viewModel.currentStep == 2 ? Icons.check_circle : Icons.arrow_forward,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(AllIndiaParcelViewModel viewModel, BuildContext context) {
    switch (viewModel.currentStep) {
      case 0:
        return _buildAddressStep(viewModel);
      case 1:
        return _buildPackageStep(viewModel);
      case 2:
        return _buildEstimateStep(viewModel, context);
      default:
        return _buildAddressStep(viewModel);
    }
  }

  Widget _buildAddressStep(AllIndiaParcelViewModel viewModel) {
    return Column(
      children: [
        // Show detailed address view if pickup address is selected
        if (viewModel.hasPickupAddress) ...[
          _buildDetailedAddressView(viewModel),
        ] else ...[
          // Show selection interface if pickup address not selected
          // Pickup Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_up,
            iconColor: AppColors.pickupIconColor,
            title: 'PICKUP AT',
            buttonText: 'Add pickup details',
            isAddressSelected: false,
            onPressed: () => viewModel.handlePickupAddressSelection(),
          ),

          const SizedBox(height: 24),

          // Delivery Section
          _buildLocationSection(
            icon: Icons.keyboard_arrow_down,
            iconColor: AppColors.deliveryIconColor,
            title: 'DELIVER TO',
            buttonText: 'Add delivery details',
            isAddressSelected: false,
            onPressed: () => viewModel.handleDeliveryAddressSelection(),
          ),
        ],

        // Distance information
        if (viewModel.distanceResult != null) ...[
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.lightBlueBackground,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.route,
                  color: AppColors.borderColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Distance',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        viewModel.distanceResult!.distanceText,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.borderColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailedAddressView(AllIndiaParcelViewModel viewModel) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left side - Icons with connecting line
              Column(
                children: [
                  // Pickup icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.pickupIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_up,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  // Connecting dashed line
                  Container(
                    width: 2,
                    height: 60,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    child: CustomPaint(
                      painter: DashedLinePainter(color: Colors.grey[400]!),
                    ),
                  ),
                  // Delivery icon
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.deliveryIconColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              // Right side - Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pickup address
                    _buildAddressDetails(
                      title: 'Pickup Address',
                      address: viewModel.pickupAddressDisplay,
                      phone: viewModel.pickupDetails?['contactPhone'] ?? '',
                      onEdit: () => viewModel.handlePickupAddressSelection(),
                    ),
                    const SizedBox(height: 32),
                    // Delivery address
                    if (viewModel.hasDeliveryAddress) ...[
                      _buildAddressDetails(
                        title: 'Delivery Address',
                        address: viewModel.deliveryAddressDisplay,
                        phone: viewModel.deliveryDetails?['contactPhone'] ?? '',
                        onEdit: () => viewModel.handleDeliveryAddressSelection(),
                      ),
                    ] else ...[
                      _buildAddDeliveryButton(viewModel),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddressDetails({
    required String title,
    required String address,
    required String phone,
    required VoidCallback onEdit,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                letterSpacing: 0.5,
              ),
            ),
            GestureDetector(
              onTap: onEdit,
              child: const Text(
                'Edit',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          address,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
          maxLines: null,
          overflow: TextOverflow.visible,
        ),
        if (phone.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            'Phone: $phone',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddDeliveryButton(AllIndiaParcelViewModel viewModel) {
    return Container(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => viewModel.handleDeliveryAddressSelection(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.deliveryIconColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Add delivery details',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildLocationSection({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String buttonText,
    required bool isAddressSelected,
    required VoidCallback onPressed,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: iconColor,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.buttonBackground,
                foregroundColor: AppColors.buttonText,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    buttonText,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Icon(
                    Icons.add_circle_outline,
                    size: 18,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageStep(AllIndiaParcelViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Package Details Header
        const Text(
          'Package Details',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),

        // Package Items List
        ...viewModel.packageItems.map((item) => _buildPackageItem(viewModel, item)),

        // Add Item Button
        const SizedBox(height: 16),
        _buildAddItemButton(viewModel),
      ],
    );
  }

  Widget _buildPackageItem(AllIndiaParcelViewModel viewModel, PackageItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header with remove button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Item ${item.itemNumber}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              if (viewModel.packageItems.length > 1)
                IconButton(
                  onPressed: () => viewModel.removePackageItem(item.id),
                  icon: const Icon(Icons.delete_outline, color: Colors.red),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Dimension text
          const Text(
            'Dimension',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Dimension grid (2x2)
          _buildDimensionGrid(viewModel, item),
          const SizedBox(height: 20),

          // Package description
          const Text(
            'Describe your package',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => viewModel.showPackageDescriptionDialog(item.id),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderColor),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Text(
                item.description.isEmpty
                    ? 'Select package type'
                    : item.description,
                style: TextStyle(
                  fontSize: 16,
                  color: item.description.isEmpty
                      ? Colors.grey.shade600
                      : Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Select contents
          const Text(
            'Select contents',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => viewModel.showPackageContentsDialog(item.id),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.borderColor),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: Text(
                item.contents.isEmpty
                    ? 'Select contents'
                    : item.contents,
                style: TextStyle(
                  fontSize: 16,
                  color: item.contents.isEmpty
                      ? Colors.grey.shade600
                      : Colors.black87,
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Package Value
          TextFormField(
            initialValue: item.value > 0 ? item.value.toString() : '',
            decoration: InputDecoration(
              hintText: 'Package Value (Max : Rs 49999)',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.borderColor),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.borderColor),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.blue),
              ),
            ),
            keyboardType: TextInputType.number,
            onChanged: (value) {
              final doubleValue = double.tryParse(value) ?? 0.0;
              if (doubleValue <= 49999) {
                viewModel.updatePackageValue(item.id, doubleValue);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDimensionGrid(AllIndiaParcelViewModel viewModel, PackageItemModel item) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: viewModel.availableDimensions.length,
      itemBuilder: (context, index) {
        final dimension = viewModel.availableDimensions[index];
        final isSelected = item.dimension.id == dimension.id;

        return GestureDetector(
          onTap: () => viewModel.updatePackageDimension(item.id, dimension),
          child: Container(
            decoration: BoxDecoration(
              color: isSelected ? Colors.lightBlue.shade100 : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue : AppColors.borderColor,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  dimension.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.blue : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  dimension.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.blue.shade700 : Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddItemButton(AllIndiaParcelViewModel viewModel) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: OutlinedButton(
        onPressed: () => viewModel.addPackageItem(),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.borderColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              color: AppColors.borderColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Add Another Item',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.borderColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(int currentStep) {
    final steps = [
      {'title': 'Address', 'icon': Icons.location_on},
      {'title': 'Package', 'icon': Icons.inventory_2_outlined},
      {'title': 'Estimate', 'icon': Icons.calendar_today_outlined},
    ];

    return Row(
      children: List.generate(steps.length * 2 - 1, (index) {
        if (index.isEven) {
          // This is a step (icon + title)
          final stepIndex = index ~/ 2;
          final isActive = stepIndex == currentStep;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Column(
              children: [
                // Icon Circle
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.inactiveStepColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    steps[stepIndex]['icon'] as IconData,
                    color: isActive || isCompleted ? Colors.yellowAccent : Colors.grey[600],
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                // Step Title
                Text(
                  steps[stepIndex]['title'] as String,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                    color: isActive
                        ? AppColors.borderColor // Dark grey black for active step text
                        : isCompleted
                            ? AppColors.buttonBackground
                            : AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          );
        } else {
          // This is a connecting line between steps
          final stepIndex = index ~/ 2;
          final isCompleted = stepIndex < currentStep;

          return Expanded(
            child: Container(
              height: 2,
              margin: const EdgeInsets.only(bottom: 32), // Align with icon center
              child: CustomPaint(
                painter: DashedLinePainter(
                  color: isCompleted
                      ? AppColors.completedStepColor
                      : AppColors.inactiveStepColor,
                ),
              ),
            ),
          );
        }
      }),
    );
  }

  // New integrated estimate step with all Local Parcel functionality
  Widget _buildEstimateStep(AllIndiaParcelViewModel viewModel, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Estimate Header
        const Text(
          'Estimate Delivery Charges',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),

        // Select your type section
        const Text(
          'Select your type',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Delivery type cards (Express and Standard)
        Row(
          children: [
            // Express Card (Left)
            Expanded(
              child: _buildDeliveryTypeCard(
                title: 'Express',
                price: '₹60',
                description: 'Same day delivery\n8am - 9pm',
                isSelected: viewModel.selectedDeliveryType == 'express',
                onTap: () => viewModel.setDeliveryType('express'),
              ),
            ),
            const SizedBox(width: 12),
            // Standard Card (Right)
            Expanded(
              child: _buildDeliveryTypeCard(
                title: 'Standard',
                price: '₹40',
                description: 'Next day delivery\n8am - 9pm',
                isSelected: viewModel.selectedDeliveryType == 'standard',
                onTap: () => viewModel.setDeliveryType('standard'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Schedule pickup section (only for Standard)
        if (viewModel.selectedDeliveryType == 'standard') ...[
          _buildSchedulePickupSection(viewModel, context),
          const SizedBox(height: 24),
        ],

        // Order Summary Section
        const Text(
          'Order Summary',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Order Type Card
        _buildOrderDetailCard(
          title: 'Order Type',
          content: Text(
            viewModel.selectedDeliveryType == 'express' ? 'Express Delivery' : 'Standard Delivery',
            style: const TextStyle(fontSize: 14, color: Colors.black87),
          ),
        ),
        const SizedBox(height: 16),

        // Pickup Address Card
        _buildOrderDetailCard(
          title: 'Pickup Address',
          onEdit: () => viewModel.goToStep(0),
          content: Text(
            viewModel.pickupAddressDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: null,
            overflow: TextOverflow.visible,
          ),
        ),
        const SizedBox(height: 16),

        // Delivery Address Card
        _buildOrderDetailCard(
          title: 'Delivery Address',
          onEdit: () => viewModel.goToStep(0),
          content: Text(
            viewModel.deliveryAddressDisplay,
            style: const TextStyle(fontSize: 14, color: Colors.black87),
            maxLines: null,
            overflow: TextOverflow.visible,
          ),
        ),
        const SizedBox(height: 16),

        // Package Items Card
        _buildOrderDetailCard(
          title: 'Package Items (${viewModel.packageItems.length})',
          onEdit: () => viewModel.goToStep(1),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (viewModel.hasPackageItems) ...[
                ...viewModel.packageItems.map((item) => _buildPackageItemSummary(item)),
              ] else ...[
                Text(
                  'No package items added',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(height: 24),

        // Terms and Conditions
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: viewModel.termsAccepted,
              onChanged: (value) => viewModel.setTermsAccepted(value ?? false),
              activeColor: Colors.blue,
            ),
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.setTermsAccepted(!viewModel.termsAccepted),
                child: const Text(
                  'I agree to the terms and conditions',
                  style: TextStyle(fontSize: 14, color: Colors.black87),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method for delivery type cards
  Widget _buildDeliveryTypeCard({
    required String title,
    required String price,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.blue : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              price,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Colors.blue[700] : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method for schedule pickup section
  Widget _buildSchedulePickupSection(AllIndiaParcelViewModel viewModel, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Schedule your pickup',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            // Date Picker
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.selectPickupDate(),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, color: Colors.grey[600], size: 20),
                      const SizedBox(width: 12),
                      Text(
                        viewModel.selectedPickupDate != null
                            ? '${viewModel.selectedPickupDate!.day}/${viewModel.selectedPickupDate!.month}/${viewModel.selectedPickupDate!.year}'
                            : 'Select Date',
                        style: TextStyle(
                          fontSize: 14,
                          color: viewModel.selectedPickupDate != null ? Colors.black87 : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Time Picker
            Expanded(
              child: GestureDetector(
                onTap: () => viewModel.selectPickupTime(),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.white,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.access_time, color: Colors.grey[600], size: 20),
                      const SizedBox(width: 12),
                      Text(
                        viewModel.selectedPickupTime != null
                            ? viewModel.selectedPickupTime!.format(context)
                            : 'Select Time',
                        style: TextStyle(
                          fontSize: 14,
                          color: viewModel.selectedPickupTime != null ? Colors.black87 : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Available time: 8am - 9pm',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // Helper method for order detail cards with optional edit functionality
  Widget _buildOrderDetailCard({
    required String title,
    required Widget content,
    VoidCallback? onEdit,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              if (onEdit != null)
                TextButton(
                  onPressed: onEdit,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.blue,
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                  child: const Text(
                    'Edit',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  // Helper method for package item summary
  Widget _buildPackageItemSummary(PackageItemModel item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Item ${item.itemNumber}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
              Text(
                item.dimension.name,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          if (item.description.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Type: ${item.description}',
              style: const TextStyle(fontSize: 12, color: Colors.black87),
            ),
          ],
          if (item.contents.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'Contents: ${item.contents}',
              style: const TextStyle(fontSize: 12, color: Colors.black87),
            ),
          ],
          if (item.value > 0) ...[
            const SizedBox(height: 4),
            Text(
              'Value: ₹${item.value.toStringAsFixed(0)}',
              style: const TextStyle(fontSize: 12, color: Colors.black87),
            ),
          ],
        ],
      ),
    );
  }

  @override
  AllIndiaParcelViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      AllIndiaParcelViewModel();
}

// Custom painter for dashed lines
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.dashWidth = 5.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0;

    double startX = 0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
