import 'package:flutter/material.dart';
import '../../common/app_colors.dart';

class AllIndiaEstimateStep extends StatelessWidget {
  final dynamic viewModel;
  const AllIndiaEstimateStep({Key? key, required this.viewModel}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Mock data for demonstration
    final cities = ['Delhi', 'Mumbai', 'Bangalore', 'Chennai'];
    final serviceTypes = ['Standard', 'Express'];
    String fromCity = viewModel.fromCity ?? 'Delhi';
    String toCity = viewModel.toCity ?? 'Mumbai';
    double weight = viewModel.weight ?? 1.0;
    String serviceType = viewModel.serviceType ?? 'Standard';
    double price = viewModel.estimatePrice ?? 250.0;
    int estimatedDays = viewModel.estimatedDays ?? 3;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Estimate Delivery Charges',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: fromCity,
                items: cities.map((city) => DropdownMenuItem(value: city, child: Text(city))).toList(),
                onChanged: (val) => viewModel.setFromCity(val),
                decoration: const InputDecoration(labelText: 'From City'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: toCity,
                items: cities.map((city) => DropdownMenuItem(value: city, child: Text(city))).toList(),
                onChanged: (val) => viewModel.setToCity(val),
                decoration: const InputDecoration(labelText: 'To City'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: weight.toString(),
                decoration: const InputDecoration(labelText: 'Weight (kg)'),
                keyboardType: TextInputType.number,
                onChanged: (val) => viewModel.setWeight(double.tryParse(val) ?? 1.0),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: serviceType,
                items: serviceTypes.map((type) => DropdownMenuItem(value: type, child: Text(type))).toList(),
                onChanged: (val) => viewModel.setServiceType(val),
                decoration: const InputDecoration(labelText: 'Service Type'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.lightBlueBackground,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Estimated Price: ₹$price', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('Estimated Delivery: $estimatedDays days', style: const TextStyle(fontSize: 16)),
            ],
          ),
        ),
      ],
    );
  }
} 