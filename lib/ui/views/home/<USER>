import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:stacked/stacked.dart';
import 'package:animated_text_kit/animated_text_kit.dart';

import 'home_viewmodel.dart';
import '../local_parcel/local_parcel_view.dart';
import '../all_india_parcel/all_india_parcel_view.dart';
import '../../common/app_colors.dart';

class HomeView extends StackedView<HomeViewModel> {
  const HomeView({super.key});

  @override
  Widget builder(
    BuildContext context,
    HomeViewModel viewModel,
    Widget? child,
  ) {
    return Scaffold(
      backgroundColor: AppColors.pageBackground, // Light grey background
      body: viewModel.isBusy
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Hero Section with animated text and animations
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.borderColor, // Dark grey black
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(24.0, 60.0, 24.0, 24.0),
                      child: Row(
                        children: [
                          // Left side - Animated text and button
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Animated text
                                SizedBox(
                                  height: 60,
                                  child: AnimatedTextKit(
                                    animatedTexts: [
                                      TypewriterAnimatedText(
                                        'Packers\nand Movers',
                                        textStyle: TextStyle(
                                          fontSize: 28,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.buttonText, // Yellow text
                                          height: 1.2,
                                        ),
                                        speed: const Duration(milliseconds: 80),
                                      ),
                                      TypewriterAnimatedText(
                                        'Fast & Safe\nDelivery',
                                        textStyle: TextStyle(
                                          fontSize: 28,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.buttonText, // Yellow text
                                          height: 1.2,
                                        ),
                                        speed: const Duration(milliseconds: 80),
                                      ),
                                      TypewriterAnimatedText(
                                        'Reliable\nTransport',
                                        textStyle: TextStyle(
                                          fontSize: 28,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.buttonText, // Yellow text
                                          height: 1.2,
                                        ),
                                        speed: const Duration(milliseconds: 80),
                                      ),
                                      TypewriterAnimatedText(
                                        'Quick\nBooking',
                                        textStyle: TextStyle(
                                          fontSize: 28,
                                          fontWeight: FontWeight.bold,
                                          color: AppColors.buttonText, // Yellow text
                                          height: 1.2,
                                        ),
                                        speed: const Duration(milliseconds: 80),
                                      ),
                                    ],
                                    repeatForever: true,
                                    pause: const Duration(milliseconds: 1500),
                                    displayFullTextOnTap: true,
                                    stopPauseOnTap: false,
                                  ),
                                ),
                                const SizedBox(height: 20),
                                // Know More button
                                ElevatedButton(
                                  onPressed: () {
                                    // TODO: Navigate to more info page
                                    debugPrint('Know More button pressed');
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.whiteBackground, // White background
                                    foregroundColor: AppColors.primaryText, // Black text
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 20,
                                      vertical: 10,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: const Text(
                                    'KNOW MORE',
                                    style: TextStyle(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 20),
                          // Right side - Service animations
                          Expanded(
                            flex: 1,
                            child: _buildCyclingServiceAnimation(),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Premium Services Section
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Premium Services',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildPremiumServicesGrid(),
                        const SizedBox(height: 24),
                        _buildRewardsSection(),
                      ],
                    ),
                  ),

                  // Rest of the content
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // User Profile Section
                        if (viewModel.userProfile != null) ...[
                          const Text(
                            'Profile Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Card(
                            elevation: 1,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'User ID: ${viewModel.userProfile!['user']?['id'] ?? 'N/A'}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Created: ${viewModel.userProfile!['user']?['createdAt'] ?? 'N/A'}',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                  const SizedBox(height: 8),
                                  if (viewModel.currentUser != null)
                                    Text(
                                      'Phone: ${viewModel.currentUser!.phoneNumber ?? 'N/A'}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.blue,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),
                        ],

                        // Action Buttons
                        ElevatedButton(
                          onPressed: viewModel.refreshProfile,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Refresh Profile',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        OutlinedButton(
                          onPressed: viewModel.signOut,
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Sign Out',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildCyclingServiceAnimation() {
    return const CyclingServiceWidget();
  }

  Widget _buildPremiumServicesGrid() {
    final List<Map<String, dynamic>> premiumServices = [
      {
        'title': 'Local Parcel',
        'icon': Icons.local_post_office,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '📦',
        'backgroundColor': Colors.white, // White background
      },
      {
        'title': 'Vehicle Booking',
        'icon': Icons.airport_shuttle,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '🚐',
        'backgroundColor': Colors.white, // White background
      },
      {
        'title': 'Packers & Movers',
        'icon': Icons.home_work,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '🏠',
        'backgroundColor': Colors.white, // White background
      },
      {
        'title': 'All India Parcel',
        'icon': Icons.public,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '🌍',
        'backgroundColor': Colors.white, // White background
      },
      {
        'title': 'Cab Booking',
        'icon': Icons.local_taxi,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '🚕',
        'backgroundColor': Colors.white, // White background
      },
      {
        'title': 'Bike Booking',
        'icon': Icons.two_wheeler,
        'color': const Color(0xFF0504aa), // Blue icon
        'emoji': '🏍️',
        'backgroundColor': Colors.white, // White background
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.1,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: premiumServices.length,
      itemBuilder: (context, index) {
        final service = premiumServices[index];
        return _buildPremiumServiceCard(context, service);
      },
    );
  }

  Widget _buildPremiumServiceCard(BuildContext context, Map<String, dynamic> service) {
    return Container(
      decoration: BoxDecoration(
        color: service['backgroundColor'] as Color, // Light blue background
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(-3, 3), // Left and bottom shadow
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            debugPrint('${service['title']} tapped');
            if (service['title'] == 'Local Parcel') {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const LocalParcelView(),
                ),
              );
            } else if (service['title'] == 'All India Parcel') {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AllIndiaParcelView(),
                ),
              );
            } else {
              // TODO: Navigate to other service details
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Service Title
                Flexible(
                  child: Text(
                    service['title'] as String,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black, // Black text as requested
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 8),
                // Service Illustration/Icon and Arrow
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Arrow icon
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: Colors.grey[400],
                    ),
                    // Service Illustration/Icon
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: AppColors.whiteBackground, // White background for icon container
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: AppColors.borderColor, // Grey black border
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          service['emoji'] as String,
                          style: const TextStyle(fontSize: 30),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRewardsSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            AppColors.buttonBackground, // Greyish black
            AppColors.buttonBackground.withValues(alpha: 0.8), // Lighter greyish black
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowColor,
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            debugPrint('Explore Porter Rewards tapped');
            // TODO: Navigate to rewards page
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Coin icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.monetization_on,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Explore Porter Rewards',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.buttonText, // Orange yellow
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Earn 2 coins for every 100 spent',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.buttonText.withValues(alpha: 0.8), // Orange yellow with transparency
                        ),
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.arrow_forward,
                  color: AppColors.buttonText, // Orange yellow
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  HomeViewModel viewModelBuilder(
    BuildContext context,
  ) =>
      HomeViewModel();

  @override
  void onViewModelReady(HomeViewModel viewModel) {
    viewModel.loadUserProfile();
    super.onViewModelReady(viewModel);
  }
}

class CyclingServiceWidget extends StatefulWidget {
  const CyclingServiceWidget({super.key});

  @override
  State<CyclingServiceWidget> createState() => _CyclingServiceWidgetState();
}

class _CyclingServiceWidgetState extends State<CyclingServiceWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Timer _timer;
  int _currentIndex = 0;

  final List<Map<String, dynamic>> _services = [
    {
      'icon': Icons.local_shipping,
      'label': 'Truck Booking',
      'color': const Color(0xFFE65100), // Vibrant Orange
      'gradient': [const Color(0xFFE65100), const Color(0xFFFF9800)],
    },
    {
      'icon': Icons.directions_car,
      'label': 'Car Booking',
      'color': const Color(0xFF1565C0), // Deep Blue
      'gradient': [const Color(0xFF1565C0), const Color(0xFF2196F3)],
    },
    {
      'icon': Icons.local_post_office,
      'label': 'Local Parcel',
      'color': const Color(0xFFD32F2F), // Bright Red
      'gradient': [const Color(0xFFD32F2F), const Color(0xFFFF5722)],
    },
    {
      'icon': Icons.public,
      'label': 'All India Parcel',
      'color': const Color(0xFF7B1FA2), // Purple
      'gradient': [const Color(0xFF7B1FA2), const Color(0xFF9C27B0)],
    },
    {
      'icon': Icons.airport_shuttle,
      'label': 'Book Vehicle',
      'color': const Color(0xFF2E7D32), // Forest Green
      'gradient': [const Color(0xFF2E7D32), const Color(0xFF4CAF50)],
    },
    {
      'icon': Icons.home_work,
      'label': 'Packers & Movers',
      'color': const Color(0xFFFF6F00), // Amber Orange
      'gradient': [const Color(0xFFFF6F00), const Color(0xFFFFC107)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _startCycling();
  }

  void _startCycling() {
    _timer = Timer.periodic(const Duration(milliseconds: 2500), (timer) {
      if (mounted) {
        setState(() {
          _currentIndex = (_currentIndex + 1) % _services.length;
        });
        _controller.reset();
        _controller.forward();
      }
    });
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentService = _services[_currentIndex];
    final gradientColors = currentService['gradient'] as List<Color>? ??
        [currentService['color'] as Color, (currentService['color'] as Color).withValues(alpha: 0.7)];

    return Center(
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 1200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.5, -0.2),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.elasticOut,
            )),
            child: FadeTransition(
              opacity: animation,
              child: ScaleTransition(
                scale: Tween<double>(begin: 0.7, end: 1.0).animate(
                  CurvedAnimation(
                    parent: animation,
                    curve: Curves.bounceOut,
                  ),
                ),
                child: RotationTransition(
                  turns: Tween<double>(begin: 0.1, end: 0.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutBack,
                    ),
                  ),
                  child: child,
                ),
              ),
            ),
          );
        },
        child: Container(
          key: ValueKey(_currentIndex),
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                gradientColors[0].withValues(alpha: 0.15),
                gradientColors[1].withValues(alpha: 0.08),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: (currentService['color'] as Color).withValues(alpha: 0.5),
              width: 2.5,
            ),
            boxShadow: [
              BoxShadow(
                color: (currentService['color'] as Color).withValues(alpha: 0.4),
                blurRadius: 15,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.9),
                blurRadius: 6,
                offset: const Offset(-3, -3),
                spreadRadius: 1,
              ),
              BoxShadow(
                color: (currentService['color'] as Color).withValues(alpha: 0.2),
                blurRadius: 25,
                offset: const Offset(0, 12),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Enhanced Animated Icon with pulsing effect
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  final pulseValue = (math.sin(_controller.value * math.pi * 2) * 0.1) + 1.0;
                  return Transform.scale(
                    scale: (0.9 + (0.4 * _controller.value)) * pulseValue,
                    child: Transform.rotate(
                      angle: _controller.value * 0.15,
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              gradientColors[0].withValues(alpha: 0.2),
                              gradientColors[1].withValues(alpha: 0.1),
                            ],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: (currentService['color'] as Color).withValues(alpha: 0.5),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                              spreadRadius: 1,
                            ),
                            BoxShadow(
                              color: (currentService['color'] as Color).withValues(alpha: 0.2),
                              blurRadius: 15,
                              offset: const Offset(0, 6),
                              spreadRadius: 0,
                            ),
                          ],
                        ),
                        child: Icon(
                          currentService['icon'] as IconData,
                          size: 26,
                          color: currentService['color'] as Color,
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 4),
              // Enhanced Animated Text with typewriter effect
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(0, 3 * (1 - _controller.value)),
                    child: Opacity(
                      opacity: _controller.value,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Text(
                          currentService['label'] as String,
                          style: TextStyle(
                            fontSize: 8.5,
                            fontWeight: FontWeight.w800,
                            color: currentService['color'] as Color,
                            letterSpacing: 0.4,
                            height: 1.1,
                            shadows: [
                              Shadow(
                                color: (currentService['color'] as Color).withValues(alpha: 0.3),
                                offset: const Offset(0.5, 0.5),
                                blurRadius: 1,
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
